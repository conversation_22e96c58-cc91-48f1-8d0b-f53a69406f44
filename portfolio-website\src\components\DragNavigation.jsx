import { useState, useRef, useEffect } from 'react'

const DragNavigation = ({ children, onNavigate, currentIndex, totalItems }) => {
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [currentX, setCurrentX] = useState(0)
  const [dragOffset, setDragOffset] = useState(0)
  const containerRef = useRef(null)
  const dragThreshold = 50 // Minimum drag distance to trigger navigation

  const handleStart = (clientX) => {
    setIsDragging(true)
    setStartX(clientX)
    setCurrentX(clientX)
    setDragOffset(0)
  }

  const handleMove = (clientX) => {
    if (!isDragging) return
    
    const diff = clientX - startX
    setCurrentX(clientX)
    setDragOffset(diff)
  }

  const handleEnd = () => {
    if (!isDragging) return
    
    const diff = currentX - startX
    
    if (Math.abs(diff) > dragThreshold) {
      if (diff > 0) {
        onNavigate('prev')
      } else {
        onNavigate('next')
      }
    }
    
    setIsDragging(false)
    setDragOffset(0)
  }

  // Mouse events
  const handleMouseDown = (e) => {
    e.preventDefault()
    handleStart(e.clientX)
  }

  const handleMouseMove = (e) => {
    handleMove(e.clientX)
  }

  const handleMouseUp = () => {
    handleEnd()
  }

  // Touch events
  const handleTouchStart = (e) => {
    handleStart(e.touches[0].clientX)
  }

  const handleTouchMove = (e) => {
    e.preventDefault()
    handleMove(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    handleEnd()
  }

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [isDragging, currentX, startX])

  return (
    <div
      ref={containerRef}
      className={`relative select-none ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      style={{
        transform: `translateX(${dragOffset * 0.3}px)`,
        transition: isDragging ? 'none' : 'transform 0.3s ease-out'
      }}
    >
      {children}
      
      {/* Drag Indicator */}
      {isDragging && (
        <div className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center">
          <div className="bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg flex items-center gap-2">
            {Math.abs(dragOffset) > dragThreshold ? (
              <>
                <svg className={`w-5 h-5 ${dragOffset > 0 ? '' : 'rotate-180'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span>Release to navigate</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16l-4-4m0 0l4-4m-4 4h18" />
                </svg>
                <span>Drag to navigate</span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default DragNavigation
