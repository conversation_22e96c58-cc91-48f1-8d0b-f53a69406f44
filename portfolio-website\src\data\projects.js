export const projects = [
  {
    id: 1,
    title: "E-Commerce Platform",
    description: "A full-stack e-commerce platform built with React, Node.js, and MongoDB. Features include user authentication, product catalog, shopping cart, and payment integration.",
    image: "/images/placeholder.svg",
    technologies: ["React", "Node.js", "MongoDB", "Express", "Stripe API", "JWT"],
    liveUrl: "https://example-ecommerce.com",
    githubUrl: "https://github.com/username/ecommerce-platform",
    featured: true,
    category: "Full Stack"
  },
  {
    id: 2,
    title: "Task Management App",
    description: "A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.",
    image: "/images/placeholder.svg",
    technologies: ["React", "Firebase", "Material-UI", "Socket.io", "Redux"],
    liveUrl: "https://example-taskapp.com",
    githubUrl: "https://github.com/username/task-management",
    featured: true,
    category: "Frontend"
  },
  {
    id: 3,
    title: "Weather Dashboard",
    description: "A responsive weather dashboard that displays current weather conditions and forecasts for multiple cities with beautiful data visualizations.",
    image: "/images/placeholder.svg",
    technologies: ["React", "Chart.js", "OpenWeather API", "CSS3", "Local Storage"],
    liveUrl: "https://example-weather.com",
    githubUrl: "https://github.com/username/weather-dashboard",
    featured: false,
    category: "Frontend"
  },
  {
    id: 4,
    title: "Blog CMS",
    description: "A content management system for blogs with markdown support, user roles, and SEO optimization features.",
    image: "/images/placeholder.svg",
    technologies: ["Next.js", "PostgreSQL", "Prisma", "NextAuth", "Tailwind CSS"],
    liveUrl: "https://example-blog.com",
    githubUrl: "https://github.com/username/blog-cms",
    featured: true,
    category: "Full Stack"
  },
  {
    id: 5,
    title: "Portfolio Website",
    description: "A modern, responsive portfolio website showcasing projects with smooth animations and interactive elements.",
    image: "/images/placeholder.svg",
    technologies: ["React", "Tailwind CSS", "Framer Motion", "Vite"],
    liveUrl: "https://example-portfolio.com",
    githubUrl: "https://github.com/username/portfolio",
    featured: false,
    category: "Frontend"
  },
  {
    id: 6,
    title: "API Gateway",
    description: "A microservices API gateway with rate limiting, authentication, and request routing capabilities.",
    image: "/images/placeholder.svg",
    technologies: ["Node.js", "Express", "Redis", "Docker", "JWT", "Rate Limiting"],
    liveUrl: "https://api.example.com",
    githubUrl: "https://github.com/username/api-gateway",
    featured: false,
    category: "Backend"
  }
];

export const personalInfo = {
  name: "John Doe",
  title: "Full Stack Developer",
  bio: "Passionate full-stack developer with 5+ years of experience creating modern web applications. I love turning complex problems into simple, beautiful designs and building scalable solutions that make a difference.",
  avatar: "/images/avatar.jpg",
  email: "<EMAIL>",
  location: "San Francisco, CA",
  socialLinks: {
    github: "https://github.com/johndoe",
    linkedin: "https://linkedin.com/in/johndoe",
    twitter: "https://twitter.com/johndoe",
    website: "https://johndoe.dev"
  },
  skills: [
    "JavaScript", "TypeScript", "React", "Node.js", "Python", 
    "MongoDB", "PostgreSQL", "AWS", "Docker", "Git"
  ]
};
